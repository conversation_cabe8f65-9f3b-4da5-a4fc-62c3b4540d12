<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="状态" prop="state">
        <el-select v-model="queryParams.state" placeholder="轮播图状态" clearable>
          <el-option
            v-for="dict in banner_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :icon="Search" @click="handleQuery">搜索</el-button>
        <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          :icon="Plus"
          @click="handleAdd"
          v-hasPermi="['vimBanner:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          :icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['vimBanner:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          :icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['vimBanner:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          :icon="Download"
          @click="handleExport"
          v-hasPermi="['vimBanner:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bannerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="轮播图" align="center" prop="image" width="120">
        <template #default="scope">
          <image-preview :src="scope.row.image" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="state">
        <template #default="scope">
          <dict-tag :options="banner_status" :value="scope.row.state"/>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            :icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['vimBanner:edit']"
          >修改</el-button>
          <el-button
            type="text"
            :icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['vimBanner:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改轮播图对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="轮播图" prop="image">
          <image-upload v-model="form.image" :limit="1"/>
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-radio-group v-model="form.state">
            <el-radio
              v-for="dict in banner_status"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="999" controls-position="right" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { listVimBanner, getVimBanner, delVimBanner, addVimBanner, updateVimBanner, exportVimBanner } from "@/api/vimBanner/index";
import { useDict } from '@/utils/dict';
import { parseTime } from '@/utils/ruoyi';
import { download } from '@/utils/request';
import { Search, Refresh, Plus, Edit, Delete, Download } from '@element-plus/icons-vue';

// 字典数据
const { banner_status } = useDict('banner_status');

// 遮罩层
const loading = ref(true);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 轮播图表格数据
const bannerList = ref([]);
// 弹出层标题
const title = ref('');
// 是否显示弹出层
const open = ref(false);
// 表单参数
const form = ref({});
// 表单校验
const rules = reactive({
  image: [
    { required: true, message: "轮播图图片不能为空", trigger: "blur" }
  ],
  state: [
    { required: true, message: "状态不能为空", trigger: "change" }
  ],
  sort: [
    { required: true, message: "排序不能为空", trigger: "blur" }
  ]
});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  state: undefined
});

const queryFormRef = ref(null);
const formRef = ref(null);

/** 查询轮播图列表 */
const getList = () => {
  loading.value = true;
  listVimBanner(queryParams.value).then(response => {
    bannerList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
};

// 取消按钮
const cancel = () => {
  open.value = false;
  reset();
};

// 表单重置
const reset = () => {
  form.value = {
    id: undefined,
    image: undefined,
    state: 1, // 1表示正常状态
    sort: 0,
    remark: undefined
  };
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields();
  }
  handleQuery();
};

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  open.value = true;
  title.value = "添加轮播图";
};

/** 修改按钮操作 */
const handleUpdate = (row) => {
  reset();
  const id = row.id || ids.value[0];
  getVimBanner(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改轮播图";
  });
};

/** 提交按钮 */
const submitForm = () => {
  formRef.value.validate(valid => {
    if (valid) {
      if (form.value.id !== undefined) {
        updateVimBanner(form.value).then(response => {
          ElMessage.success("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addVimBanner(form.value).then(response => {
          ElMessage.success("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = (row) => {
  const idsToDelete = row.id || ids.value;
  ElMessageBox.confirm('是否确认删除轮播图编号为"' + idsToDelete + '"的数据项?').then(() => {
    return delVimBanner(idsToDelete);
  }).then(() => {
    getList();
    ElMessage.success("删除成功");
  }).catch(() => {});
};

/** 导出按钮操作 */
const handleExport = () => {
  ElMessageBox.confirm('是否确认导出所有轮播图数据项?').then(() => {
    ElMessage.loading({ message: "正在导出数据，请稍后...", duration: 0 });
    return exportVimBanner(queryParams.value);
  }).then(response => {
    download(response.msg);
    ElMessage.closeAll();
  }).catch(() => {});
};

onMounted(() => {
  getList();
});
</script> 