<template>
  <div class="statistics-container">
    <div class="statistics-header">
      <div class="welcome-section">
        <h1 class="welcome-title">财务数据统计</h1>
        <p class="welcome-subtitle">{{ currentDate }}</p>
      </div>
    </div>

    <div class="filter-container">
      <!-- 主播数据筛选控件 -->
      <div class="filter-item">
        <label class="filter-label">数据筛选：</label>
        <el-select
            v-model="streamerFilter"
            placeholder="选择数据范围"
            @change="handleStreamerFilterChange"
            :size="isMobileView ? 'small' : 'default'"
            :popper-append-to-body="true"
            :popper-class="isMobileView ? 'mobile-select-dropdown' : ''"
        >
          <el-option label="包含主播数据" value="all"/>
          <el-option label="排除主播数据" value="exclude"/>
          <el-option label="仅主播数据" value="only"/>
        </el-select>
      </div>

      <!-- 精确时间选择器 -->
      <div class="filter-item">
        <label class="filter-label">时间范围：</label>
        <el-date-picker
            v-model="dateTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期时间"
            end-placeholder="结束日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleDateTimeRangeChange"
            :shortcuts="dateTimeShortcuts"
            :size="isMobileView ? 'small' : 'default'"
            :popper-options="{
              modifiers: [
                {
                  name: 'computeStyles',
                  options: {
                    adaptive: true,
                    gpuAcceleration: false
                  }
                }
              ]
            }"
            :teleported="true"
            :unlink-panels="isMobileView"
            :default-view="isMobileView ? 'date' : 'date'"
        />
      </div>

      <!-- 导出功能按钮 -->
      <div class="filter-item">
        <el-button
            type="success"
            :icon="Download"
            @click="handleExport"
            :loading="exportLoading"
            :disabled="!dateTimeRange || dateTimeRange.length !== 2"
            :size="isMobileView ? 'small' : 'default'"
            class="export-button"
        >
          导出数据
        </el-button>
      </div>
    </div>

    <div class="stat-cards-container">
      <!-- 数据统计卡片 - 根据筛选条件显示不同的卡片 -->
      <div 
        class="stat-card" 
        v-for="(stat, index) in visibleStatCards" 
        :key="index" 
        :style="`--card-color: ${stat.color}`"
      >
        <div class="stat-icon">
          <el-icon>
            <component :is="stat.icon"/>
          </el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </div>
<!--    <el-row :gutter="20">
      &lt;!&ndash; 最新开箱记录 &ndash;&gt;
      <el-col :xs="24" :sm="24" :md="14" :lg="14">
        <el-card class="data-card" v-loading="loading.latestRecords">
          <template #header>
            <div class="card-header">
              <span>最新开箱记录</span>
              <el-button type="primary" link @click="viewAllRecords">查看全部</el-button>
            </div>
          </template>
          <el-table :data="latestRecords" style="width: 100%" stripe>
            <el-table-column label="用户" min-width="100">
              <template #default="scope">
                <div class="user-info">
                  <el-avatar :size="30" :src="scope.row.avatar"></el-avatar>
                  <span>{{ scope.row.username }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="boxName" label="盲盒名称" min-width="120"/>
            <el-table-column prop="prize" label="开出物品" min-width="120"/>
            <el-table-column prop="time" label="时间" min-width="120"/>
            <el-table-column label="稀有度" width="100">
              <template #default="scope">
                <el-tag :type="getRarityType(scope.row.rarity)">{{ getRarityName(scope.row.rarity) }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      &lt;!&ndash; 热门盲盒排行 &ndash;&gt;
      <el-col :xs="24" :sm="24" :md="10" :lg="10">
        <el-card class="data-card" v-loading="loading.hotBoxes">
          <template #header>
            <div class="card-header">
              <span>热门盲盒排行</span>
              <el-button type="primary" link @click="viewAllBoxes">查看全部</el-button>
            </div>
          </template>
          <div class="top-boxes-list">
            <div v-for="(box, index) in topBoxes" :key="index" class="top-box-item">
              <div class="box-rank">{{ index + 1 }}</div>
              <div class="box-image">
                <el-image :src="box.image" fit="cover"></el-image>
              </div>
              <div class="box-info">
                <div class="box-name">{{ box.name }}</div>
                <div class="box-stats">
                  <span>销量: <strong>{{ box.saleVolume }}</strong></span>
                  <span>价格: <strong>{{ box.price }}元</strong></span>
                </div>
              </div>
              <div class="box-trend">
                <el-progress
                    :percentage="box.popularity"
                    :color="getColorByPercentage(box.popularity)"
                    :stroke-width="6"
                    :show-text="false">
                </el-progress>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>-->
  </div>
</template>

<script setup name="FinanceStatistics">
import {computed, onMounted, ref, watch, onUnmounted} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage} from 'element-plus'
import {Download} from '@element-plus/icons-vue'

// 导入自定义组合式函数
import {useDateParams} from '@/composables/useDateParams'
import {useDashboardData} from '@/composables/useDashboardData'
import {useDashboardCharts} from '@/composables/useDashboardCharts'
import * as XLSX from 'xlsx'

const router = useRouter()

// 新增的响应式数据
const streamerFilter = ref('all') // 主播筛选条件
const dateTimeRange = ref([]) // 精确时间范围
const exportLoading = ref(false) // 导出加载状态
const isMobileView = ref(window.innerWidth <= 768) // 是否为移动视图

// 使用日期参数组合式函数
const {dateRange, dateParams} = useDateParams()

// 时间范围变量
const timeRange = ref('day')

// 使用图表组合式函数
const {
  initMainChart,
  initPieChart,
} = useDashboardCharts({timeRange})

// 使用仪表盘数据组合式函数
const {
  loading,
  statCards,
  latestRecords,
  topBoxes,
  statisticsCache,
  fetchBoxTrend,
  loadAllData,
  fetchLatestStatisticsForExport,
  groupStatisticsByCategory,
  refreshStatisticsWithFilter
} = useDashboardData({
  dateParams,
  timeRange,
  streamerFilter,
  dateTimeRange
})

// 当前日期
const currentDate = computed(() => {
  const now = new Date()
  return now.toLocaleDateString('zh-CN', {year: 'numeric', month: 'long', day: 'numeric', weekday: 'long'})
})

// 根据筛选条件过滤显示的统计卡片
const visibleStatCards = computed(() => {
  // 根据筛选条件决定显示哪些卡片
  let visibleCategories = []
  switch (streamerFilter.value) {
    case 'exclude':
      visibleCategories = ['用户相关统计', '其他业务统计']
      break
    case 'only':
      visibleCategories = ['主播相关统计', '其他业务统计']
      break
    case 'all':
    default:
      visibleCategories = ['合计统计', '其他业务统计']
      break
  }
  
  // 过滤出要显示的卡片，排除被标记为 hidden 的卡片
  return statCards.value.filter(card => 
    visibleCategories.includes(card.category) && !card.hidden
  )
})

// 方法
const getRarityType = (rarity) => {
  // 根据后端返回的等级数字映射到对应的稀有度类型
  switch (rarity) {
    case 1:
      return 'danger'    // 传说
    case 2:
      return 'warning'   // 保密
    case 3:
      return 'success'   // 隐秘
    case 4:
      return 'info'      // 受限
    default:
      return ''
  }
}

const getColorByPercentage = (percentage) => {
  if (percentage > 90) return '#F56C6C'
  if (percentage > 75) return '#E6A23C'
  if (percentage > 60) return '#67C23A'
  return '#909399'
}

// 精确时间选择器的快捷选项
const dateTimeShortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 999)
      return [start, end]
    }
  },
  {
    text: '昨天',
    value: () => {
      const start = new Date()
      start.setDate(start.getDate() - 1)
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setDate(end.getDate() - 1)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    }
  },
  {
    text: '最近7天',
    value: () => {
      const start = new Date()
      start.setDate(start.getDate() - 6)
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 999)
      return [start, end]
    }
  },
  {
    text: '最近30天',
    value: () => {
      const start = new Date()
      start.setDate(start.getDate() - 29)
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 999)
      return [start, end]
    }
  }
]

// 方法：处理主播筛选变化
const handleStreamerFilterChange = () => {
  ElMessage.info(`已切换到：${getStreamerFilterLabel(streamerFilter.value)}`)

  // 使用缓存数据重新过滤，无需重新请求API
  const refreshed = refreshStatisticsWithFilter()
  
  if (refreshed) {
    console.log('已使用缓存数据重新过滤统计卡片')
  } else {
    console.log('无缓存数据，已重新请求数据')
    // 如果没有缓存数据，则会在refreshStatisticsWithFilter中重新请求
  }
}

// 方法：处理精确时间范围变化
const handleDateTimeRangeChange = () => {
  // 时间范围变化时，强制刷新缓存
  refreshAllData()
}

// 工具方法：获取主播筛选标签
const getStreamerFilterLabel = (value) => {
  const labels = {
    'all': '包含主播数据',
    'exclude': '排除主播数据',
    'only': '仅主播数据'
  }
  return labels[value] || '未知'
}

// 方法：刷新所有数据
const refreshAllData = () => {
  // 使用增强的参数重新获取所有统计数据
  loadAllData()
      .then(({fetchBoxTrend, fetchBoxDistribution, fetchLatestBoxRecords, fetchHotBoxes}) => {
        // 重新加载图表数据
        fetchBoxTrend(initMainChart, true)
        fetchBoxDistribution(initPieChart)
        // 重新加载表格数据
        fetchLatestBoxRecords()
        fetchHotBoxes()
      })
}

const viewAllRecords = () => {
  router.push('/VimOrderBoxSys/VimOrderBoxs')
}

const viewAllBoxes = () => {
  router.push('/vimBoxSys/vimBoxs')
}

// 导出数据功能
const handleExport = async () => {
  if (!dateTimeRange.value || dateTimeRange.value.length !== 2) {
    ElMessage.warning('请先选择时间范围')
    return
  }

  exportLoading.value = true

  try {
    // 主动查询最新统计数据，不使用缓存
    const startDate = dateTimeRange.value[0]
    const endDate = dateTimeRange.value[1]
    const streamerFilterLabel = getStreamerFilterLabel(streamerFilter.value)

    ElMessage.info('正在获取最新统计数据...')

    // 调用统计报告API获取最新数据（不受筛选条件影响，获取所有数据）
    const latestStatisticsData = await fetchLatestStatisticsForExport(
      startDate,
      endDate,
      streamerFilter.value
    )

    if (!latestStatisticsData || latestStatisticsData.length === 0) {
      ElMessage.warning('当前时间范围内暂无统计数据，建议选择其他时间范围')
      exportLoading.value = false
      return
    }

    // 按分类分组数据
    const groupedData = groupStatisticsByCategory(latestStatisticsData)

    // 创建工作簿
    const wb = XLSX.utils.book_new()

    // 创建单个综合工作表（包含所有分类数据）
    createComprehensiveSheet(wb, {
      startDate,
      endDate,
      streamerFilterLabel,
      totalItems: latestStatisticsData.length,
      categories: Object.keys(groupedData)
    }, groupedData)

    // 生成文件名，包含时间范围信息
    const startDateStr = startDate.split(' ')[0]
    const endDateStr = endDate.split(' ')[0]
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_')
    const fileName = `Dashboard统计数据_${startDateStr}_至_${endDateStr}_${timestamp}.xlsx`

    // 导出文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功！共导出 ${latestStatisticsData.length} 项统计数据，按 ${Object.keys(groupedData).length} 个分类分组显示在单个工作表中`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}

// Excel导出辅助函数
/**
 * 创建单个综合工作表（包含所有分类数据）
 */
const createComprehensiveSheet = (workbook, options, groupedData) => {
  const {startDate, endDate, streamerFilterLabel, totalItems, categories} = options

  // 初始化工作表数据数组
  const sheetData = [
    ['VIM系统Dashboard统计数据报告'],
    [],
    ['导出信息'],
    ['时间周期', `${startDate} 至 ${endDate}`],
    ['导出时间', new Date().toLocaleString('zh-CN')],
    ['数据说明', '导出包含所有类型数据，不受筛选条件限制'],
    ['统计项目总数', `${totalItems}项`],
    ['数据分类数量', `${categories.length}个分类`],
    [],
    []
  ]

  // 定义分类显示顺序
  const categoryOrder = ['用户相关统计', '主播相关统计', '合计统计', '其他业务统计']

  // 记录分类标题行位置，用于设置样式
  const categoryTitleRows = []
  const dataHeaderRows = []

  // 按顺序添加每个分类的数据
  categoryOrder.forEach((category, categoryIndex) => {
    if (groupedData[category] && groupedData[category].length > 0) {
      // 记录分类标题行位置
      categoryTitleRows.push(sheetData.length)

      // 添加分类标题
      sheetData.push([`${categoryIndex + 1}. ${category}`])

      // 记录数据表头行位置
      dataHeaderRows.push(sheetData.length)

      // 添加数据表头
      sheetData.push(['序号', '统计项目', '数值', '单位', '备注'])

      // 添加该分类下的统计数据
      let itemIndex = 1; // 用于正确显示序号
      groupedData[category].forEach((item) => {
        // 跳过"详情"类型的数据
        if (item.stat_name && item.stat_name.includes('详情')) {
          return;
        }
        
        const formattedValue = formatStatValue(item.stat_value, item.unit)
        sheetData.push([
          itemIndex++,
          item.stat_name || '未知项目',
          formattedValue,
          item.unit || '',
          item.category || ''
        ])
      })

      // 添加分类间的空行分隔
      sheetData.push([])
    }
  })

  // 创建工作表
  const ws = XLSX.utils.aoa_to_sheet(sheetData)

  // 设置列宽
  ws['!cols'] = [
    {wch: 8},  // 序号列
    {wch: 25}, // 统计项目列
    {wch: 20}, // 数值列
    {wch: 8},  // 单位列
    {wch: 15}  // 备注列
  ]

  // 设置合并单元格
  if (!ws['!merges']) ws['!merges'] = []

  // 主标题合并单元格
  ws['!merges'].push({s: {r: 0, c: 0}, e: {r: 0, c: 4}})

  // 导出信息标题合并单元格
  ws['!merges'].push({s: {r: 2, c: 0}, e: {r: 2, c: 4}})

  // 分类标题合并单元格
  categoryTitleRows.forEach(row => {
    ws['!merges'].push({s: {r: row, c: 0}, e: {r: row, c: 4}})
  })

  XLSX.utils.book_append_sheet(workbook, ws, '统计数据报告')
}

/**
 * 格式化统计值显示
 */
const formatStatValue = (value, unit) => {
  if (value === null || value === undefined) {
    return '0'
  }

  const numValue = Number(value)

  if (unit === '元' || unit === '¥') {
    return numValue.toLocaleString('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    })
  } else if (unit === '%') {
    return `${numValue.toFixed(2)}%`
  } else {
    return numValue.toLocaleString()
  }
}

// 监听窗口大小变化，更新移动视图状态
const handleResize = () => {
  isMobileView.value = window.innerWidth <= 768
}

// 生命周期钩子
onMounted(() => {
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)

  // 初始化数据
  loadAllData()
    .then(({fetchBoxTrend, fetchBoxDistribution, fetchLatestBoxRecords, fetchHotBoxes}) => {
      // 加载图表数据
      // fetchBoxTrend(initMainChart)
      // fetchBoxDistribution(initPieChart)
      // 加载表格数据
      // fetchLatestBoxRecords()
      // fetchHotBoxes()
    })
})

onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)
})

// 监听时间范围变化，更新趋势图
watch(timeRange, () => {
  fetchBoxTrend(initMainChart)
})
</script>

<style lang="scss" scoped>
.statistics-container {
  padding: 20px;
  
  .statistics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .welcome-section {
      .welcome-title {
        font-size: 24px;
        margin: 0;
        color: var(--el-text-color-primary);
      }
      
      .welcome-subtitle {
        margin: 5px 0 0;
        color: var(--el-text-color-secondary);
      }
    }
  }
  
  .filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--el-bg-color-overlay);
    border-radius: 4px;
    box-shadow: var(--el-box-shadow-light);
    
    .filter-item {
      display: flex;
      align-items: center;
      
      .filter-label {
        margin-right: 8px;
        white-space: nowrap;
      }
    }
    
    .export-button {
      margin-left: auto;
    }
  }
  
  .stat-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
    
    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: var(--el-bg-color-overlay);
      border-radius: 4px;
      box-shadow: var(--el-box-shadow-light);
      border-left: 4px solid var(--card-color, #409EFF);
      
      .stat-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background-color: var(--card-color, #409EFF);
        color: white;
        margin-right: 15px;
        
        .el-icon {
          font-size: 24px;
        }
      }
      
      .stat-info {
        flex: 1;
        
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
          color: var(--el-text-color-primary);
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }
  
  .chart-section {
    margin-bottom: 20px;
  }
  
  .chart-card, .data-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .chart-container {
      height: 350px;
    }
  }
  
  .user-info {
    display: flex;
    align-items: center;
    
    .el-avatar {
      margin-right: 8px;
    }
  }
  
  .top-boxes-list {
    .top-box-item {
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid var(--el-border-color-lighter);
      
      &:last-child {
        border-bottom: none;
      }
      
      .box-rank {
        width: 24px;
        height: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: var(--el-color-primary);
        color: white;
        border-radius: 50%;
        margin-right: 10px;
        font-weight: bold;
      }
      
      .box-image {
        width: 60px;
        height: 60px;
        margin-right: 15px;
        
        .el-image {
          width: 100%;
          height: 100%;
          border-radius: 4px;
        }
      }
      
      .box-info {
        flex: 1;
        
        .box-name {
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        .box-stats {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
      
      .box-trend {
        width: 100px;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .statistics-container {
    padding: 10px;
    
    .filter-container {
      flex-direction: column;
      gap: 10px;
      
      .filter-item {
        width: 100%;
        
        .el-select, .el-date-picker {
          width: 100%;
        }
      }
      
      .export-button {
        margin-left: 0;
        width: 100%;
      }
    }
    
    .stat-cards-container {
      grid-template-columns: 1fr;
    }
  }
}
</style>
